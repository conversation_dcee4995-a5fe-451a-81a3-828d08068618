package main

import (
	"context"
	"fmt"
	"time"

	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	front_end_token_service_pb "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	ripb "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/rs/zerolog/log"
)

var invitationResolutionStatusCounter = promauto.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_auth_central_invitation_resolution_status_total",
		Help: "Total number of invitation resolution attempts by final status",
	},
	[]string{"status"},
)

// InvitationResolutionProcessor is responsible for processing invitation resolution messages from
// the async-ops pubsub topic.
type InvitationResolutionProcessor struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	stripeClient            StripeClient
	requestInsightPublisher ripublisher.RequestInsightPublisher
	orbConfig               *OrbConfig
	orbClient               orb.OrbClient
	auditLogger             *audit.AuditLogger
}

func NewInvitationResolutionProcessor(
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	stripeClient StripeClient,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	orbConfig *OrbConfig,
	orbClient orb.OrbClient,
	auditLogger *audit.AuditLogger,
) (*InvitationResolutionProcessor, error) {
	return &InvitationResolutionProcessor{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		stripeClient:            stripeClient,
		requestInsightPublisher: requestInsightPublisher,
		orbConfig:               orbConfig,
		orbClient:               orbClient,
		auditLogger:             auditLogger,
	}, nil
}

// Note that the returned error from this function is about whether we want to retry the message,
// and not necessarily whether there was an error during processing. Nil means we don't want to
// retry.
func (p *InvitationResolutionProcessor) Process(
	ctx context.Context,
	msg *auth_internal.ResolveInvitationsMessage,
) (returnErr error) {
	log.Info().
		Str("invitation_resolution_id", msg.InvitationResolutionId).
		Msg("Processing invitation resolution message")
	invitationResolutionDAO := p.daoFactory.GetInvitationResolutionDAO()
	req := msg.GetResolveInvitationsRequest()
	invitationResolutionID := msg.InvitationResolutionId

	// Read the invitation resolution record from BigTable. It is quite likely that this will fail on
	// the first try since we publish to the pub/sub queue before writing this record to BigTable.
	// Retry a few times to avoid a long backoff while we wait for pub/sub to retry.
	var invitationResolution *auth_entities.InvitationResolution
	var err error
	maxAttempts := 5
	sleepDuration := time.Millisecond * 200
	for attempt := 0; attempt < maxAttempts; attempt++ {
		invitationResolution, err = invitationResolutionDAO.Get(ctx, invitationResolutionID)
		if err == nil && invitationResolution != nil {
			break
		}
		log.Warn().Err(err).Msgf(
			"Failed to fetch invitation resolution record, attempt %d/%d", attempt+1, maxAttempts)
		time.Sleep(sleepDuration)
	}
	if err != nil {
		return fmt.Errorf("failed to fetch invitation resolution record after %d attempts", maxAttempts)
	} else if invitationResolution == nil {
		// This can happen if the retries above beat the write to BigTable.
		// TODO(jacqueline): We should probably have some time window after which we just give up.
		return fmt.Errorf("invitation resolution %s not found", invitationResolutionID)
	}

	// Make sure the resolution is PENDING.
	if invitationResolution.Status != auth_entities.InvitationResolution_PENDING {
		log.Info().Msgf(
			"Invitation resolution %s is %s. Not processing.",
			invitationResolutionID, invitationResolution.Status)
		return nil
	}

	// From this point on, the processing code can set newResolutionStatus to update the status before
	// returning.
	var newResolutionStatus auth_entities.InvitationResolution_Status
	defer func() {
		// Update the invitation resolution record with the new status.
		if newResolutionStatus != auth_entities.InvitationResolution_UNKNOWN {
			invitationResolution.Status = newResolutionStatus
			_, err := invitationResolutionDAO.Update(ctx, invitationResolution)
			if err != nil {
				// Make sure we return an error in this case so that pub/sub retries and we don't end up
				// with an orphaned resolution.
				returnErr = fmt.Errorf("failed to update invitation resolution record: %w", err)
			}

			invitationResolutionStatusCounter.WithLabelValues(newResolutionStatus.String()).Inc()
		}
	}()

	// Fetch all the referenced invitations so we can error early if any are invalid. If we can't find
	// any referenced invitations we mark the entire resolution as failed and return nil to prevent
	// further retries; this shouldn't happen so using a big hammer should be fine.
	var acceptedInvitation *auth_entities.TenantInvitation
	declinedInvitations := make([]*auth_entities.TenantInvitation, 0, len(req.DeclineInvitationIds))
	if req.GetAcceptInvitationId() != "" {
		acceptedInvitation, err = p.getInvitationByID(ctx, req.GetAcceptInvitationId())
		if err != nil {
			log.Error().Err(err).Msgf("Failed to fetch accepted invitation %s", req.GetAcceptInvitationId())
			newResolutionStatus = auth_entities.InvitationResolution_ERROR
			return nil
		} else if acceptedInvitation.Status != auth_entities.TenantInvitation_PENDING {
			// Ensure that the invitation is PENDING. Otherwise somebody could potentially declind an
			// invitation first and then accept it later to get around seat count checks.
			log.Error().Msgf("Accepted invitation %s is not PENDING", acceptedInvitation.Id)
			newResolutionStatus = auth_entities.InvitationResolution_ERROR
			return nil
		}
	}
	for _, invitationID := range req.DeclineInvitationIds {
		invitation, err := p.getInvitationByID(ctx, invitationID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to fetch declined invitation %s", invitationID)
			newResolutionStatus = auth_entities.InvitationResolution_ERROR
			return nil
		}
		// We don't bother checking for a PENDING status here because there's no harm in marking a
		// previously processed invitation declined. If we get here somehow err on the side of not
		// surfacing an error to the user.
		declinedInvitations = append(declinedInvitations, invitation)
	}

	// Update all declined invitations as DECLINED.
	for _, invitation := range declinedInvitations {
		invitationDAO := p.daoFactory.GetTenantInvitationDAO(invitation.TenantId)
		invitation.Status = auth_entities.TenantInvitation_DECLINED
		_, err := invitationDAO.Update(ctx, invitation)
		if err != nil {
			return fmt.Errorf("failed to update declined invitation: %w", err)
		}
		log.Info().Msgf(
			"%s declined invitation %s to tenant %s",
			invitation.InviteeEmail, invitation.Id, invitation.TenantId)

		// Record the declined invitation in request insight.
		event := ripublisher.NewTenantEvent()
		event.Event = &ripb.TenantEvent_DeclineInvitation{
			DeclineInvitation: &ripb.DeclineInvitation{
				InvitationId: invitation.Id,
				InviteeEmail: invitation.InviteeEmail,
			},
		}
		riErr := p.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
			// HACK: We know that self-serve tenants' names are the same as their ID. Save ourselves a
			// database lookup and just use the id for both values here.
			TenantId:   invitation.TenantId,
			TenantName: invitation.TenantId,
		}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish DeclineInvitation event")
		}
	}

	// Handle the accepted invitation, if any.
	if acceptedInvitation != nil {
		// Disallow this operation if the user's email is associated with an enterprise tenant. This
		// should be unlikely since we check this condition when invitations are created, but we want
		// to protect against the case where the enterprise tenant was created after the invitation.
		tenant, err := p.tenantMap.GetTenantForEmailDomain(acceptedInvitation.InviteeEmail)
		if err != nil {
			return fmt.Errorf("failed to get tenant for email domain: %w", err)
		} else if tenant != nil && tenant.Tier == tw_proto.TenantTier_ENTERPRISE {
			newResolutionStatus = auth_entities.InvitationResolution_ERROR
			log.Warn().Msgf(
				"User %s was invited to tenant %s but has an email associated with enterprise tenant %s. Not resolving invitation %s.",
				acceptedInvitation.InviteeEmail, acceptedInvitation.TenantId, tenant.Name, acceptedInvitation.Id)
			return nil
		}

		user, err := p.tenantMap.GetUserByEmailAddress(ctx, acceptedInvitation.InviteeEmail)
		if err != nil {
			return fmt.Errorf("failed to get user information: %w", err)
		}

		if user != nil {
			// Use MoveUserToTenant, which will remove the user from their other tenants.
			err = p.tenantMap.MoveUserToTenant(ctx, user.Id, acceptedInvitation.TenantId)
			if err != nil {
				return fmt.Errorf("failed to move user to tenant: %w", err)
			}

			// Cancel Orb subscription - always cancel immediately when joining a team
			if user.OrbSubscriptionId != "" && p.orbClient != nil {
				log.Info().Msgf("Cancelling existing Orb subscription for augment user %s, orb subscription ID %s", user.Id, user.OrbSubscriptionId)

				// First, check if subscription is already scheduled for cancellation and unschedule it
				subscription, err := p.orbClient.GetUserSubscription(ctx, user.OrbSubscriptionId, nil)
				if err != nil {
					return fmt.Errorf("failed to get user subscription: %w", err)
				}

				// If subscription is already scheduled for cancellation, unschedule it first
				if !subscription.EndDate.IsZero() {
					log.Info().Msgf("Subscription %s is already scheduled for cancellation, unscheduling first", user.OrbSubscriptionId)
					err = p.orbClient.UnschedulePendingSubscriptionCancellation(ctx, user.OrbSubscriptionId, nil)
					if err != nil {
						return fmt.Errorf("failed to unschedule existing subscription cancellation: %w", err)
					}
				}

				// Now cancel immediately regardless of previous state
				err = p.orbClient.CancelOrbSubscription(ctx, user.OrbSubscriptionId, orb.PlanChangeImmediate, nil, nil)
				if err != nil {
					return fmt.Errorf("failed to cancel existing Orb subscription: %w", err)
				}

			} else {
				log.Info().Msgf("No existing Orb subscription to cancel for augment user %s", user.Id)
			}
		} else {
			// Since the user is new, if the team is on a trial, we should give their trial credits to the subscription.
			log.Info().Msgf("Tenant ID %s", acceptedInvitation.TenantId)
			if p.orbClient != nil {
				tenantSubscriptionMappingDAO := p.daoFactory.GetTenantSubscriptionMappingDAO()
				tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, acceptedInvitation.TenantId)
				if err != nil {
					return fmt.Errorf("failed to get tenant subscription mapping: %w", err)
				}
				if tenantSubscriptionMapping == nil {
					return fmt.Errorf("tenant subscription mapping not found for tenant %s", acceptedInvitation.TenantId)
				}
				orbSubscription, err := p.orbClient.GetUserSubscription(ctx, tenantSubscriptionMapping.OrbSubscriptionId, &orb.ItemIds{
					SeatsID:            p.orbConfig.SeatsItemID,
					IncludedMessagesID: p.orbConfig.IncludedMessagesItemID,
				})
				if err != nil {
					return fmt.Errorf("failed to get tenant subscription: %w", err)
				}
				log.Info().Msgf("Tenant subscription plan ID %s", orbSubscription.ExternalPlanID)
				plan := p.orbConfig.findPlan(orbSubscription.ExternalPlanID)
				if plan == nil {
					log.Error().Str("plan_id", orbSubscription.ExternalPlanID).Msg("Failed to find plan")
					return fmt.Errorf("failed to find plan")
				}
				// Only give credits if the team is on the trial plan
				if plan.Features.PlanType == PlanTypePaidTrial {
					// Do not give extra credits if they have already gained credits for the max number of seats accepting an invite
					// The -1 is because the original user already counts towards the max number of seats
					if int(tenantSubscriptionMapping.TrialCreditsAwardedCount) >= plan.Features.MaxSeats-1 {
						log.Info().
							Int("trial_credits_awarded_count", int(tenantSubscriptionMapping.TrialCreditsAwardedCount)).
							Int("max_seats", plan.Features.MaxSeats).
							Msgf("Team already has maximum trial credits, not giving additional credits")
					} else {
						log.Info().Msgf("Adding additional trial messages to team for new user %s", acceptedInvitation.InviteeEmail)
						planInfo, err := p.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: p.orbConfig.IncludedMessagesItemID, SeatsID: p.orbConfig.SeatsItemID}, &tenantSubscriptionMapping.OrbSubscriptionId, nil)
						if err != nil {
							return fmt.Errorf("failed to get Orb plan info: %w", err)
						}
						// Create idempotency key for additional credits
						addtlCreditsIdempotencyKey := fmt.Sprintf("%s-additional-trial-credits", msg.InvitationResolutionId)
						err = p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
							OrbSubscriptionID: tenantSubscriptionMapping.OrbSubscriptionId,
							PriceOverride: orb.OrbPriceOverrides{
								PriceID:  planInfo.IncludedMessagesPriceID,
								Quantity: float64(orbSubscription.CurrentFixedQuantities.IncludedMessages) + (planInfo.MessagesPerSeat), // add an additional user's worth of credits
							},
							UpdateTimeType: orb.PlanChangeImmediate,
						}, &addtlCreditsIdempotencyKey)
						if err != nil {
							return fmt.Errorf("failed to give additional user messages for new user: %w", err)
						}

						// Update the number of granted trial credits
						tenantSubscriptionMapping.TrialCreditsAwardedCount++
						_, err = tenantSubscriptionMappingDAO.Update(ctx, tenantSubscriptionMapping)
						if err != nil {
							return fmt.Errorf("failed to update trial credit grant count: %w", err)
						}
					}
				}
			}

			// Use EnsureUserInTenant to create the user and add them to the tenant.
			user, err = p.tenantMap.EnsureUserInTenant(
				ctx, nil, acceptedInvitation.InviteeEmail, acceptedInvitation.TenantId, "", front_end_token_service_pb.TenantEnsureMode_TENANT_ENSURE_MODE_ADD_IF_EMPTY)
			if err != nil {
				return fmt.Errorf("failed to ensure user in tenant: %w", err)
			}
		}

		// Update the accepted invitation as ACCEPTED.
		invitationDAO := p.daoFactory.GetTenantInvitationDAO(acceptedInvitation.TenantId)
		acceptedInvitation.Status = auth_entities.TenantInvitation_ACCEPTED
		_, err = invitationDAO.Update(ctx, acceptedInvitation)
		if err != nil {
			return fmt.Errorf("failed to update accepted invitation: %w", err)
		}

		log.Info().Msgf(
			"%s accepted invitation %s to tenant %s",
			acceptedInvitation.InviteeEmail, acceptedInvitation.Id, acceptedInvitation.TenantId)

		// Record the accepted invitation in request insight.
		event := ripublisher.NewTenantEvent()
		event.Event = &ripb.TenantEvent_AcceptInvitation{
			AcceptInvitation: &ripb.AcceptInvitation{
				InvitationId: acceptedInvitation.Id,
				User:         user,
			},
		}
		riErr := p.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
			// HACK: We know that self-serve tenants' names are the same as their ID. Save ourselves a
			// database lookup and just use the id for both values here.
			TenantId:   acceptedInvitation.TenantId,
			TenantName: acceptedInvitation.TenantId,
		}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish AcceptInvitation event")
		}
	}
	newResolutionStatus = auth_entities.InvitationResolution_SUCCESS
	if acceptedInvitation != nil {
		p.auditLogger.WriteAuditLog(
			"",
			"",
			acceptedInvitation.TenantId,
			fmt.Sprintf("User %s successfully accepted invitation to tenant %s", acceptedInvitation.InviteeEmail, acceptedInvitation.TenantId),
		)
	}
	return nil
}

// Wrapper around getting a single invitation by ID. Returns an error if this fails (including if
// the given ID is associated with more than one invitation, which shouldn't happen but is
// technically possible with our key structure).
func (p *InvitationResolutionProcessor) getInvitationByID(
	ctx context.Context, invitationID string,
) (*auth_entities.TenantInvitation, error) {
	invitationDAO := p.daoFactory.GetTenantInvitationDAO("")
	invitation := make([]*auth_entities.TenantInvitation, 0, 1)
	err := invitationDAO.FindAllForInvitationID(ctx, invitationID, func(i *auth_entities.TenantInvitation) bool {
		invitation = append(invitation, i)
		return true
	})
	if err != nil {
		log.Error().Err(err).Msgf("Failed to fetch invitation %s", invitationID)
		return nil, fmt.Errorf("failed to fetch invitation: %w", err)
	}

	if len(invitation) == 0 {
		log.Error().Msgf("Failed to fetch invitation %s", invitationID)
		return nil, fmt.Errorf("invitation %s not found", invitationID)
	} else if len(invitation) > 1 {
		log.Error().Msgf("Found %d invitations for ID %s", len(invitation), invitationID)
		return nil, fmt.Errorf("found %d invitations for ID %s", len(invitation), invitationID)
	}

	return invitation[0], nil
}
